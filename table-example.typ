// 表格使用示例
#import "settings/table.typ": styled-table, load-csv-simple, simple-table

= 表格示例

== 使用 simple-table (最简单，推荐)

使用分号分隔数据，无需为每个单元格添加括号：

#simple-table(
  columns: 6,
  data: "列1; 列2; 列3; 列4; 列5; 列6; 数据1; 数据2; 数据3; 数据4; 数据5; 数据6; 更多数据1; 更多数据2; 更多数据3; 更多数据4; 更多数据5; 更多数据6"
)

== 使用 styled-table (传统方式)

如果您更喜欢原来的语法，也可以继续使用：

#styled-table(
  columns: 6,
  [列1], [列2], [列3], [列4], [列5], [列6],
  [数据1], [数据2], [数据3], [数据4], [数据5], [数据6],
  [更多数据1], [更多数据2], [更多数据3], [更多数据4], [更多数据5], [更多数据6],
)

== 带标题的表格

使用 simple-table 添加标题：

#simple-table(
  columns: 6,
  caption: "实验数据统计表",
  data: "列1; 列2; 列3; 列4; 列5; 列6; 数据1; 数据2; 数据3; 数据4; 数据5; 数据6; 更多数据1; 更多数据2; 更多数据3; 更多数据4; 更多数据5; 更多数据6"
)

或者使用 styled-table：

#styled-table(
  columns: 6,
  caption: "实验数据统计表",
  [列1], [列2], [列3], [列4], [列5], [列6],
  [数据1], [数据2], [数据3], [数据4], [数据5], [数据6],
  [更多数据1], [更多数据2], [更多数据3], [更多数据4], [更多数据5], [更多数据6],
)

== 使用说明

=== simple-table (推荐)
- 使用分号 `;` 分隔所有数据
- 数据中可以包含逗号，不会影响分列
- 参数说明：
  - `columns`: 列数（必需）
  - `data`: 用分号分隔的所有表格数据（必需）
  - `caption`: 表格标题（可选，如果提供会自动编号）

=== styled-table (传统方式)
- 每个单元格需要用方括号 `[]` 包围
- 参数说明：
  - `columns`: 列数（必需）
  - `caption`: 表格标题（可选，如果提供会自动编号）
  - 后面跟表格内容，按行列顺序排列

=== 共同特性
两种函数都会自动：
- 让表格与页面等宽
- 居中显示
- 应用三线表样式（顶线、表头下线、底线）
- 所有列等宽分布
- 设置合适的内边距和字体大小

=== CSV加载
#load-csv-simple("../assets/data/result.csv", caption: "实验结果")
