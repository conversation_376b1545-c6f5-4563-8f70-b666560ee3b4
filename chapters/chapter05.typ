// ================================================================
//                   第五章 结论与展望
// ================================================================
#import "../settings/heading.typ": *  


#new-chapter()
#chapter[第五章 结论与展望]


// ================================================================
//                   5.1 研究结论
// ================================================================

#section[研究结论]



本节将以高度凝练的语言，分点回顾并重申全篇论文最关键的研究结论。例如：(1) 成功构建了一个双层优化框架，并通过超亲水表面工程显著提升了湿壁旋风采样器的性能，使其优于商业化标准设备。(2) 成功开发了一个基于深度学习的自动化识别模型，首次实现了对多种无染色致敏花粉的高精度、快速检测，其性能媲美甚至超越了传统金标准。(3) 成功设计并验证了一个集成化的微流控-视觉传感系统原型，首次在单一小型化平台中实现了对空气致敏原的“形态学-生物化学”多模态在线监测。


// ================================================================
//                   5.2 研究创新点
// ================================================================
#section[研究创新点]
本节将从三个层面阐述本论文的独创性贡献。在理论与方法学层面，提出了“场景-装置”双层优化这一系统工程思想，以及“免染色识别”这一颠覆性分析范式。在技术与工具层面，贡献了一款性能卓越的新型采样器、一个高质量的开源花粉图像数据集、一个即用型的AI识别模型，以及两款功能创新的微流控芯片。在系统集成层面，构建了本领域首个具备多模态检测能力的自动化监测原型，为下一代环境健康传感器的发展提供了重要的技术蓝图和工程实践。




// ================================================================
//                   5.3 研究展望
// ================================================================
#section[研究展望]



本节将综合各章节的局限性，并从更宏观的视角提出未来研究的展望。例如：(1) 拓展监测广度：将AI识别模型拓展至更多种类的花粉和真菌孢子，将免疫分析通道拓展至病毒等其他致敏原。(2) 提升系统鲁棒性：专注于解决微流控抗堵塞和试剂板上存储等关键工程难题，进行长期的户外实地测试。(3) 迈向网络化与智能化：将多个监测站点进行物联网（IoT）部署，构建区域性的高时空分辨率致敏原预警网络，并结合气象数据与机器学习模型，实现从“监测”到“预测”的跨越。