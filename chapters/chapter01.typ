// ================================================================
//                     第一章 绪论
// ================================================================
#import "../settings/heading.typ": *
#import "../settings/table.typ": styled-table, load-csv-simple, simple-table


#new-chapter()
#chapter[第一章 绪论]



// ================================================================
//                   1.1 研究背景与意义
// ================================================================

#section[研究背景与意义]

本节将从宏观视角切入，首先阐述全球气候变化与城市化进程如何加剧了空气中致敏性生物气溶胶（allergenic bioaerosols）的暴露风险。具体将论述三类关键致敏原：病毒（如呼吸道合胞病毒RSV，其气溶胶传播机制对易感人群构成威胁）、致敏性真菌（如曲霉菌Aspergillus、念珠菌Candida，其孢子与代谢产物是哮喘和过敏性肺炎的重要诱因）、以及致敏花粉（Pollen，季节性过敏性鼻炎的主要来源）。将引用流行病学数据，强调这些生物气溶胶对全球公共卫生造成的沉重负担和对个体生活质量的严重影响。基于此，本节将明确指出，发展能够快速（rapid）、精准（accurate）、自动化（automated）地对上述致敏原进行在线监测的技术，不仅是过敏性疾病预防与控制的关键环节，更是构建智能化公共卫生预警体系的基石。








// ================================================================
//                   1.2 空气生物气溶胶监测技术：现状与挑战
// ================================================================

#section[空气生物气溶胶监测技术：现状与挑战]

本节将对现有监测技术进行一次批判性的综述。在采样技术层面，将系统分析传统方法，如Hirst型孢子采样器（Burkard trap）的原理、优点（行业金标准）与缺点（时间分辨率低、依赖人工分析）；撞击法采样器（Andersen cascade impactor）的原理、优点（可按粒径分级）与缺点（培养依赖性、颗粒物损伤）；以及液体冲击法采样器（如AGI-30, BioSampler）的原理、优点（样本为液体，利于后续分析）与缺点（采样效率对流速敏感、存在蒸发问题）。在分析技术层面，将评述光学显微镜检（耗时、依赖专家、无法区分形态相似物种）、微生物培养法（仅能检测可培养微生物、周期长）、以及分子生物学方法（如PCR，灵敏度高但无法区分活体/死体、前处理复杂）。最后，本节将总结出现有技术链条存在的三大核心瓶颈：时效性滞后、分析流程割裂与劳动密集、以及系统集成度与自动化水平低下，从而为本研究的切入点提供坚实的背景支撑。

// ================================================================
//                   1.3 研究问题与主要内容
// ================================================================
#section[研究问题与主要内容]

基于上述挑战，本节将凝练出本论文旨在回答的一个中心科学问题：“如何构建一个从样本采集到多模态分析的全流程自动化、小型化系统，以克服传统监测方法的固有瓶颈，实现对空气致敏颗粒物的在线、高时效性监测？” 为系统性地回答此问题，本研究将分解为三个逻辑递进的研究层面：
    1.  高效采样前端的构建与优化：如何从根本上提升采样效率，特别是颗粒物向液体样本的转移效率？
    2.  快速、精准识别算法的开发：如何利用人工智能技术，取代传统耗时的人工镜检，实现对关键致敏原（花粉）的自动化、免染色识别？
    3.  集成化、多模态分析系统的实现：如何将优化的采样前端与智能识别算法，通过微流控技术进行无缝集成，并拓展至生化检测维度，构建一个功能完整的在线监测原型？





// ================================================================
//                   1.4 研究技术路线
// ================================================================
#section[研究技术路线]

本节将以一张精心设计的技术路线图为核心，直观展示本论文的内在逻辑。该图将清晰地描绘出从第二章（采样器双层优化）、第三章（AI识别算法开发）到第四章（微流控-视觉集成系统）的研究进程。图中将用箭头标示各章节之间的技术传承与输入/输出关系，例如：第二章优化的采样器是第三章花粉采集的工具，同时也是第四章集成系统的采样单元；第三章开发的算法是第四章视觉传感模块的核心软件。最后，简要阐述每一章在本研究全局中的定位与贡献，勾勒出论文的完整叙事结构。