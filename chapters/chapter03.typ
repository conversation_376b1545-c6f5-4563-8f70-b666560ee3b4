// ================================================================
//                   第三章 基于深度学习的空气致敏花粉自动化识别
// ================================================================
#import "../settings/heading.typ": *
#import "../settings/table.typ": styled-table, load-csv-simple, simple-table


#new-chapter()
#chapter[第三章 基于深度学习的空气致敏花粉自动化识别]



// ================================================================
//                   3.1 引言
// ================================================================
#section[引言]

本引言将直接切入传统孢粉学（aeropalynology）监测的痛点。首先描述依赖于人工显微镜检的“金标准”流程，并系统性地阐述其在时效性（slowness）、专业技能依赖性（expertise-dependency）和主观性（subjectivity）三大方面的根本缺陷。随后，引入计算机视觉，特别是深度学习中的目标检测（object detection）技术，作为解决上述挑战的革命性工具。最后，明确提出本章的核心创新点与挑战：开发一个能够直接处理无染色（stain-free）样本的识别框架，以期最大程度地简化样品前处理流程，实现真正的快速检测。


本章将介绍与研究相关的技术背景...



// ================================================================
//                   3.2 材料与方法
// ================================================================
#section[材料与方法]

#subsection[实验材料与仪器]

详细介绍技术原理A...




// ================================================================
//                   3.2.1 真实世界致敏花粉的采集与样本库构建
// ================================================================
#subsection[真实世界致敏花粉的采集与样本库构建]

明确指出花粉样本来源于第二章优化的采样器，确保了样本与未来应用的现实相关性。详细列出在中国三个不同生态地理区域（内蒙古包头-温带草原气候；河北新乡-温带季风气候；上海宝山-亚热带季风气候）采集的14种主要致敏花粉的物种名录。







// ================================================================
//                   3.2.2 高分辨率无染色花粉图像数据集的生成与标注
// ================================================================
#subsection[高分辨率无染色花粉图像数据集的生成与标注]

从制备载玻片（强调未使用任何染色剂）、显微镜参数标准化设置（物镜、NA值、相机分辨率、白平衡），到图像采集的完整SOP。描述使用专业标注软件（如CVAT）进行人工标注的过程，包括边界框（bounding box）的绘制原则与类别标签的赋予，以及为确保标注质量而实施的交叉复核机制。




详细介绍技术原理B...
// ================================================================
//                   3.2.3 基于YOLO架构的目标检测模型
// ================================================================
#subsection[基于YOLO架构的目标检测模型]

#subsubsection[模型选择与网络架构配置]

明确所选用的YOLO具体版本（如YOLOv8-L），并阐述其网络结构特点。详细列出模型训练的关键参数：数据集划分比例（训练/验证/测试，如8:1:1）、数据增强（data augmentation）的具体策略（如随机旋转、亮度调整、mosaic等）、优化器选择（如AdamW）、学习率调度策略以及训练的总轮次（epochs）。

#subsubsection[超参数选择与调优]
详细介绍技术原理B...

#subsubsection[损失函数设计]

详细介绍技术原理B...
#subsubsection[训练策略与收敛分析]

详细介绍技术原理B...




// ================================================================
//                   3.2.4 性能评估与比较验证方案
// ================================================================
#subsection[性能评估与比较验证方案]




定义用于评估模型性能的量化指标（精确率Precision, 召回率Recall, mAP\@0.5, mAP\@0.5:0.95）。核心是设计一个严格的三方盲测实验：使用同一批未知的、复杂的无染色混合花粉图像，分别由(1) 本研究训练的YOLO模型、(2) 人类孢粉学专家（在无染色条件下）、以及(3) 人类专家（在经过品红染色处理的平行样本上，作为金标准）进行识别与计数。



// ================================================================
//                   第三节 结果与讨论
// ================================================================
#section[结果与讨论]


// ================================================================
//                   3.3.1 中国代表性区域致敏花粉图像数据集的特征
// ================================================================
#subsection[中国代表性区域致敏花粉图像数据集的特征]

以图表形式展示数据集中各类花粉的样本数量分布。呈现一张包含所有14种花粉代表性图像的拼接图（a panel figure），并辅以文字，简要描述其关键形态学特征以及彼此间易于混淆的难点，为后续模型的性能讨论提供背景。


#subsubsection[14种花粉形态学特征统计]
详细介绍技术原理B...
详细介绍技术原理B...详细介绍技术原理B...详细介绍技术原理B...
详细介绍技术原理B...
#load-csv-simple("../assets/data/result.csv", caption: "实验结果")



// ================================================================
//                   3.3.2 YOLO模型在单类花粉识别任务中的性能表现
// ================================================================

#subsection[YOLO模型在单类花粉识别任务中的性能表现]

报告模型在独立的测试集上的mAP值，并展示混淆矩阵（confusion matrix），以直观地分析模型对不同类别花粉的识别能力，特别是对哪些形态相似的类别容易产生误判。




// ================================================================
//                   3.3.3 自动化模型在复杂混合样本中的表现
// ================================================================

#subsection[自动化模型在复杂混合样本中的表现]


以清晰的图表对比三方盲测的结果，包括总识别准确率、对各个类别的F1-score等。关键结论应为：YOLO模型在处理无染色样本时的性能，显著优于同样处理无染色样本的人类专家，并且其性能指标已非常接近甚至在某些类别上超越了依赖染色的“金标准”方法。




// 设置为横向页面
#page(flipped: true)[

#load-csv-simple("../assets/data/result.csv", caption: "实验结果")


#load-csv-simple("../assets/data/line.csv", caption: "实验结果")

#load-csv-simple("../assets/data/line.csv", caption: "实验结果")

]





// 你的横向内容（比如宽表格、图表等）
#styled-table(
  columns: 6,
  caption: "实验数据统计表",
  [列1], [列2], [列3], [列4], [列5], [列6],
  [数据1], [数据2], [数据3], [数据4], [数据5], [数据6],
  [更多数据1], [更多数据2], [更多数据3], [更多数据4], [更多数据5], [更多数据6],
)

#load-csv-simple("../assets/data/line.csv", caption: "实验结果")


本研究中使用的主要工具包括...




// ================================================================
//                   3.3.4 自动化识别对现代孢粉学监测的意义与启示
// ================================================================



#subsection[自动化识别对现代孢粉学监测的意义与启示]

基于上述结果，深入讨论本研究成果如何从根本上回应了引言中提出的三大挑战。着重论述“免染色”识别的实现，对简化流程、降低成本、加速响应时间的革命性意义。展望该技术作为未来自动化、网络化花粉监测网络核心引擎的巨大潜力。


// ================================================================
//                   第四节 本章小结
// ================================================================
#section[本章小结]

总结本研究在构建高质量真实世界数据集、实现高精度免染色识别方面的创新。


深入讨论数据集的物种与地理覆盖局限性、模型对破损/遮挡花粉的处理能力、以及在区分同属内极相似物种上的潜在挑战。


当前技术的发展趋势分析...



#simple-table(
  columns: 6,
  data: "列1; 列2; 列3; 列4; 列5; 列6; 数据1; 数据2; 数据3; 数据4; 数据5; 数据6; 更多数据1; 更多数据2; 更多数据3; 更多数据4; 更多数据5; 更多数据6"
)