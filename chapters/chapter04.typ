// ================================================================
//                   第四章 多模态致敏颗粒物在线监测系统
// ================================================================
#import "../settings/heading.typ": *
#import "../settings/table.typ": styled-table, load-csv-simple, simple-table


#new-chapter()
#chapter[第四章 多模态致敏颗粒物在线监测系统]



// ================================================================
//                   4.1 引言
// ================================================================
#section[引言]

首先总结前两章已分别解决了“高效采样”和“智能识别”这两个核心科学问题。然后，明确指出将这些分立的技术模块（discrete technology modules）转化为一个能够现场部署、自主运行的集成化系统（integrated system），是实现真正“在线监测”的决定性一步。引入微流控技术作为实现系统小型化（miniaturization）、自动化（automation）与功能多样化（functional diversification）的关键使能技术。最后，提出本章的核心目标：构建并验证一个集成了空气采样、微流控处理、视觉识别和免疫分析的多模态（multi-modal）监测系统原型。



本章将介绍与研究相关的技术背景..


// ================================================================
//                   4.2 材料与方法
// ================================================================
#section[材料与方法]

// ================================================================
//                   4.2.1 系统总体架构与自动化工作流程
// ================================================================


#subsection[系统总体架构与自动化工作流程]

以一张详细的系统架构图（schematic diagram）为核心，展示采样单元（第二章成果）、流体驱动单元（蠕动泵、微型阀）、微流控分析单元、视觉传感单元以及中央控制与数据处理单元（如嵌入式计算机）之间的物理连接与信息流。以时序图（timing diagram）描述系统从“采样-泵送-分析-数据输出”的完整自动化工作循环。




// ================================================================
//                   4.2.2 边缘计算视觉传感模块的设计与构建
// ================================================================
#subsection[边缘计算视觉传感模块的设计与构建]

详细列出硬件清单，包括嵌入式计算平台（如NVIDIA Jetson Orin Nano）、CMOS图像传感器、紧凑型光学镜头组以及可程控LED照明阵列。描述如何通过3D打印技术设计并制作模块化的、具有光路对齐和芯片固定功能的机械外壳。阐述将第三章训练的YOLO模型通过TensorRT等工具进行优化，并部署到该边缘设备上的软件流程。


// ================================================================
//                   4.2.3 双通道微流控分析芯片的设计与制备
// ================================================================
#subsection[双通道微流控分析芯片的设计与制备]


阐述芯片采用标准软光刻技术（soft lithography）与PDMS-玻璃键合的制备方法。

#subsubsection[花粉动态捕获与成像芯片]


提供该芯片的AutoCAD设计图，标注V型鱼骨微结构阵列的关键几何参数（如沟槽宽度、角度）。解释其流体力学工作原理，即通过诱导横向微涡流实现对颗粒的减速与聚焦。



#subsubsection[自动化微流控免疫分析芯片]
展示该芯片的多层复杂设计。阐述蛇形通道如何用于延长反应孵育时间；微柱阵列如何通过增大比表面积来增强信号；以及集成的试剂储库、混合器、清洗缓冲液通道和废液出口如何实现ELISA流程的全自动化。

// ================================================================
//                   4.2.4 集成系统性能验证方案
// ================================================================
#subsection[集成系统性能验证方案]

分步描述验证流程。(1) 模块验证：使用静态载玻片验证视觉模块的性能。(2) 通道一验证：泵入花粉悬液，验证V型鱼骨芯片的动态捕获与识别能力。(3) 通道二验证：泵入真菌标志物标准品，绘制剂量-反应曲线，计算微流控ELISA的检测限（LOD）和线性范围。




// ================================================================
//                   第三节 结果与讨论
// ================================================================
#section[结果与讨论]


// ================================================================
//                   4.3.1 视觉传感模块的性能标定与算法部署验证
// ================================================================
#subsection[视觉传感模块的性能标定与算法部署验证]

呈现紧凑型平台与科研级显微镜所采集图像的对比，并以表格形式量化对比YOLO模型在两套图像上的mAP值，证明小型化并未牺牲关键性能。

// ================================================================
//                   4.3.2 花粉在线动态捕获与识别性能
// ================================================================

#subsection[花粉在线动态捕获与识别性能]

展示花粉在V型鱼骨芯片中运动轨迹的序列图像，直观证明其减速效果。报告在连续流条件下，系统对花粉的捕获率与识别准确率。

// ================================================================
//                   4.3.3 基于微柱阵列的ELISA芯片定量结果研究
// ================================================================

#subsection[基于微柱阵列的ELISA芯片定量结果研究]

呈现高质量的ELISA剂量-反应曲线图，报告计算出的LOD值，并将其与传统96孔板法进行性能对比，突出其在灵敏度、速度和试剂消耗上的优势。




// ================================================================
//                   4.3.4 一个多模态、现场可部署系统的构建与价值
// ================================================================
#subsection[一个多模态、现场可部署系统的构建与价值]

本节为升华部分。重点讨论“多模态”的科学价值：视觉通道提供了对花粉等大尺寸颗粒形态学（morphological）信息，而免疫分析通道则提供了对真菌等小尺寸、形态不可辨颗粒的生物化学（biochemical）信息。论述这两种正交的信息维度如何互为补充，使系统能够生成远比单一技术更全面、更可靠的空气致敏原图谱，从而实现从“监测”到“洞察”的飞跃。





// ================================================================
//                   第四节 本章小结
// ================================================================
#section[本章小结]


总结本系统在集成度、自动化、小型化以及多模态检测能力方面的突破。

深入讨论微流控通道的长期抗堵塞能力、生化试剂的板上长期稳定存储、流体交叉污染的风险控制，以及系统在复杂户外环境中的适应性等关键工程挑战。

