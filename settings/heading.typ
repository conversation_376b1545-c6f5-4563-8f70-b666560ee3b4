// ================================================================
//                    标题样式文件 (Heading Styles)
// ================================================================

// --- 自定义标题函数 ---
#let chapter(title) = {
  // 创建隐藏标题供目录使用
  { show heading: _ => {}; heading(level: 1, outlined: true, numbering: none)[#title] }
  // 显示样式：22pt，加粗，居中，段前段后间距
  v(1.5em)
  { set text(font: ("STIX Two Text", "Noto Serif CJK SC"), size: 18pt, weight: "bold")
    set par(leading: 25pt)
    align(center, title) }
  v(0.5em)
}

#let section(title) = {
  counter("section").step()
  counter("subsection").update(0)  // 重置三级标题计数器
  counter("subsubsection").update(0)  // 重置四级标题计数器
  // 创建隐藏标题和显示内容
  context {
    let chapter-num = counter(heading).get().at(0)
    let section-num = counter("section").get().at(0)
    let full-title = str(chapter-num) + "." + str(section-num) + " " + title
    // 隐藏标题供目录使用
    { show heading: _ => {}; heading(level: 2, outlined: true, numbering: none)[#full-title] }
    // 显示样式：16pt，加粗
    v(0.25em)
    { set text(font: ("STIX Two Text", "Noto Serif CJK SC"), size: 14pt, weight: "bold")
      set par(leading: 18pt, first-line-indent: 0em)
      full-title }
  }
}

#let subsection(title) = {
  counter("subsection").step()
  counter("subsubsection").update(0)  // 重置四级标题计数器
  // 创建隐藏标题和显示内容
  context {
    let chapter-num = counter(heading).get().at(0)
    let section-num = counter("section").get().at(0)
    let subsection-num = counter("subsection").get().at(0)
    let full-title = str(chapter-num) + "." + str(section-num) + "." + str(subsection-num) + " " + title
    // 隐藏标题供目录使用
    { show heading: _ => {}; heading(level: 3, outlined: true, numbering: none)[#full-title] }
    // 显示样式：14pt，正常字重
    v(0.25em)
    { set text(font: ("STIX Two Text", "Noto Serif CJK SC"), size: 14pt)
      set par(leading: 12pt, first-line-indent: 0em)
      full-title }
  }
}

#let subsubsection(title) = {
  counter("subsubsection").step()
  // 四级标题：不进入目录，使用 (1)、(2) 编号
  context {
    let subsubsection-num = counter("subsubsection").get().at(0)
    let full-title = "(" + str(subsubsection-num) + ") " + title
    // 显示样式：14pt，正常字重，不进入目录，添加3em左边距
    v(0.25em)
    { set text(font: ("STIX Two Text", "Noto Serif CJK SC"), size: 14pt)
      set par(leading: 20pt, first-line-indent: 0em)
      h(3em)  // 向右缩进3em（两个中文字符）
      full-title }
  }
}

// --- 章节管理函数 ---
#let new-chapter() = {
  counter("section").update(0)
  counter("subsection").update(0)
  counter("subsubsection").update(0)
  counter(heading).step()
}
