# 博士学位论文：用于空气致敏颗粒物在线监测的集成化、多模态传感方法学研究

---

## 第一章 绪论

### 1.1 研究背景与意义

本节将从宏观视角切入，首先阐述全球气候变化与城市化进程如何加剧了空气中致敏性生物气溶胶的暴露风险。具体将论述三类关键致敏原：病毒（如呼吸道合胞病毒）、致敏性真菌（如曲霉菌、念珠菌）以及致敏花粉。将引用流行病学数据，强调这些生物气溶胶对全球公共卫生造成的沉重负担和对个体生活质量的严重影响。基于此，本节将明确指出，发展能够快速、精准、自动化地对上述致敏原进行在线监测的技术，不仅是过敏性疾病预防与控制的关键环节，更是构建智能化公共卫生预警体系的基石。

### 1.2 空气生物气溶胶监测技术：现状与挑战

本节将对现有监测技术进行一次批判性的综述。在采样技术层面，将系统分析传统方法，如Hirst型孢子采样器的原理、优点与缺点；撞击法采样器的原理、优点与缺点；以及液体冲击法采样器的原理、优点与缺点。在分析技术层面，将评述光学显微镜检、微生物培养法以及分子生物学方法。最后，本节将总结出现有技术链条存在的三大核心瓶颈：时效性滞后、分析流程割裂与劳动密集、以及系统集成度与自动化水平低下，从而为本研究的切入点提供坚实的背景支撑。

### 1.3 本研究的核心科学问题与主要内容

基于上述挑战，本节将凝练出本论文旨在回答的一个中心科学问题：“如何构建一个从样本采集到多模态分析的全流程自动化、小型化系统，以克服传统监测方法的固有瓶颈，实现对空气致敏颗粒物的在线、高时效性监测？” 为系统性地回答此问题，本研究将分解为三个逻辑递进的研究层面：

1. 高效采样前端的构建与优化：如何从根本上提升采样效率，特别是颗粒物向液体样本的转移效率？
2. 快速、精准识别算法的开发：如何利用人工智能技术，取代传统耗时的人工镜检，实现对关键致敏原（花粉）的自动化、免染色识别？
3. 集成化、多模态分析系统的实现：如何将优化的采样前端与智能识别算法，通过微流控技术进行无缝集成，并拓展至生化检测维度，构建一个功能完整的在线监测原型？

### 1.4 论文结构与技术路线

本节将以一张精心设计的技术路线图为核心，直观展示本论文的内在逻辑。该图将清晰地描绘出从第二章（采样器双层优化）、第三章（AI识别算法开发）到第四章（微流控-视觉集成系统）的研究进程。图中将用箭头标示各章节之间的技术传承与输入/输出关系，例如：第二章优化的采样器是第三章花粉采集的工具，同时也是第四章集成系统的采样单元；第三章开发的算法是第四章视觉传感模块的核心软件。最后，简要阐述每一章在本研究全局中的定位与贡献，勾勒出论文的完整叙事结构。

---

## 第二章 新型生物气溶胶采样系统的双层优化与性能研究

### 2.1 引言

本引言将聚焦于湿壁旋风采样技术。首先阐述其高流速、连续采样、样本液态化的理论优势。随后，直接切入其核心技术瓶颈——高速气流剪切导致的液膜失稳问题，并指出这是限制其真实捕获效率，特别是分析物液相转移率的关键物理机制。最后，提出本章的核心研究策略：建立一个创新的“场景-装置”双层优化框架，旨在系统性地提升采样系统的综合性能。

### 2.2 材料与方法

#### 2.2.1 采样系统设计与构建：高速湿壁旋风采样器

详细描述采样器的三维结构设计、关键尺寸参数、材料选择，以及核心驱动单元（22,200 rpm无刷直流电机）的规格与控制方式。

#### 2.2.2 场景级优化：基于计算流体力学的部署策略仿真

阐述为上海国际机场150平方米采样舱建立的计算流体力学模型。明确模型的几何边界、网格划分、湍流模型（如k-ε模型）以及离散相模型的设置，其目标是模拟颗粒物在舱内的输运与分布，以指导采样器的最优空间部署。

#### 2.2.3 装置级优化：内壁超亲水改性与流体体积法液膜动力学仿真

详述内壁超亲水涂层的制备工艺（如溶胶-凝胶法或化学气相沉积法）及表面接触角表征。阐述针对采样器内部建立的流体体积法多相流模型，其核心是通过设定高、低两种接触角工况，来精细模拟并对比有/无涂层时，气-液界面的动态演化行为。

#### 2.2.4 系统综合性能评估方案：真实环境验证与基准比较

描述在机场真实环境中进行病毒气溶胶采集的实验流程。详述在受控实验舱中，使用荧光微球验证分析物液相转移率提升的实验方案，以及使用雾化的致敏真菌标志物，将优化后的采样器与两种商业化金标准设备进行头对头性能比较的实验设计。

### 2.3 结果与讨论

#### 2.3.1 场景级优化策略的计算流体力学仿真与现场验证

呈现机场舱室的流场与颗粒物浓度分布云图，并据此论证采样器最佳布点的合理性。报告现场采样成功检出目标病毒的结果，并讨论这如何验证了计算流体力学指导部署这一场景级优化策略的有效性。

#### 2.3.2 装置级优化：超亲水表面对液膜稳定性和捕获效率的提升机制

本节为核心。首先，展示流体体积法仿真结果，通过对比动态图像，直观呈现无涂层内壁的液膜破裂、形成干区，以及超亲水内壁上连续、稳定液膜的形成。随后，呈现荧光微球的捕获实验结果，通过定量数据，证实超亲水改性显著提升了颗粒物的捕获效率，并讨论这一实验结果与仿真预测的高度一致性，从而建立起从表面工程到液膜稳定再到效率提升的完整机理链条。

#### 2.3.3 综合性能基准比较：与商用采样器的对比评估

以柱状图清晰展示本研究开发的采样器与两种商用设备在采集致敏真菌标志物浓度上的显著差异。通过统计学分析，量化证明本系统在采集效率上的优越性，并讨论其优势的来源。

### 2.4 本章小结与展望

#### 2.4.1 方法优势

总结双层优化框架的创新性，以及超亲水改性在解决行业共性难题上的突破。

#### 2.4.2 方法学局限性

深入讨论涂层长期稳定性、系统能耗与噪音、验证气溶胶代表性、以及收集液蒸发管理等问题。

---

## 第三章 基于深度学习的空气致敏花粉自动化识别：一个面向无染色样本的快速检测框架

### 3.1 引言

本引言将直接切入传统孢粉学监测的痛点。首先描述依赖于人工显微镜检的“金标准”流程，并系统性地阐述其在时效性、专业技能依赖性和主观性三大方面的根本缺陷。随后，引入计算机视觉，特别是深度学习中的目标检测技术，作为解决上述挑战的革命性工具。最后，明确提出本章的核心创新点与挑战：开发一个能够直接处理无染色样本的识别框架，以期最大程度地简化样品前处理流程，实现真正的快速检测。

### 3.2 材料与方法

#### 3.2.1 真实世界致敏花粉的采集与样本库构建

明确指出花粉样本来源于第二章优化的采样器，确保了样本与未来应用的现实相关性。详细列出在中国三个不同生态地理区域（内蒙古包头、河北新乡、上海宝山）采集的14种主要致敏花粉的物种名录。

#### 3.2.2 高分辨率无染色花粉图像数据集的生成与标注

详述从制备载玻片（强调未使用任何染色剂）、显微镜参数标准化设置，到图像采集的完整操作流程。描述使用专业标注软件进行人工标注的过程，包括边界框的绘制原则与类别标签的赋予，以及为确保标注质量而实施的交叉复核机制。

#### 3.2.3 基于YOLO架构的目标检测模型设计与训练

明确所选用的YOLO具体版本，并阐述其网络结构特点。详细列出模型训练的关键参数：数据集划分比例、数据增强的具体策略、优化器选择、学习率调度策略以及训练的总轮次。

#### 3.2.4 性能评估与多方比较验证方案

定义用于评估模型性能的量化指标（精确率, 召回率, 平均精度均值）。核心是设计一个严格的三方盲测实验：使用同一批未知的、复杂的无染色混合花粉图像，分别由 (1) 本研究训练的YOLO模型、(2) 人类孢粉学专家（在无染色条件下）、以及 (3) 人类专家（在经过品红染色处理的平行样本上，作为金标准）进行识别与计数。

### 3.3 结果与讨论

#### 3.3.1 中国代表性区域致敏花粉图像数据集的构建与特征分析

以图表形式展示数据集中各类花粉的样本数量分布。呈现一张包含所有14种花粉代表性图像的拼接图，并辅以文字，简要描述其关键形态学特征以及彼此间易于混淆的难点，为后续模型的性能讨论提供背景。

#### 3.3.2 YOLO模型在基准测试集上的性能评估

报告模型在独立的测试集上的平均精度均值，并展示混淆矩阵，以直观地分析模型对不同类别花粉的识别能力，特别是对哪些形态相似的类别容易产生误判。

#### 3.3.3 核心验证：自动化模型与人类专家及金标准的性能对决

本节为高潮。以清晰的图表对比三方盲测的结果，包括总识别准确率、对各个类别的F1分数等。关键结论应为：YOLO模型在处理无染色样本时的性能，显著优于同样处理无染色样本的人类专家，并且其性能指标已非常接近甚至在某些类别上超越了依赖染色的“金标准”方法。

#### 3.3.4 讨论：自动化识别对现代孢粉学监测的意义与启示

基于上述结果，深入讨论本研究成果如何从根本上回应了引言中提出的三大挑战。着重论述“免染色”识别的实现，对简化流程、降低成本、加速响应时间的革命性意义。展望该技术作为未来自动化、网络化花粉监测网络核心引擎的巨大潜力。

### 3.4 本章小结与展望

#### 3.4.1 方法优势

总结本研究在构建高质量真实世界数据集、实现高精度免染色识别方面的创新。

#### 3.4.2 方法学局限性

深入讨论数据集的物种与地理覆盖局限性、模型对破损或遮挡花粉的处理能力、以及在区分同属内极相似物种上的潜在挑战。

---

## 第四章 多模态致敏颗粒物在线监测系统：集成化微流控-视觉传感平台的构建与验证

### 4.1 引言

本引言将作为承前启后的关键。首先总结前两章已分别解决了“高效采样”和“智能识别”这两个核心科学问题。然后，明确指出将这些分立的技术模块转化为一个能够现场部署、自主运行的集成化系统，是实现真正“在线监测”的决定性一步。引入微流控技术作为实现系统小型化、自动化与功能多样化的关键使能技术。最后，提出本章的核心目标：构建并验证一个集成了空气采样、微流控处理、视觉识别和免疫分析的多模态监测系统原型。

### 4.2 材料与方法

#### 4.2.1 系统总体架构与自动化工作流程

以一张详细的系统架构图为核心，展示采样单元、流体驱动单元、微流控分析单元、视觉传感单元以及中央控制与数据处理单元之间的物理连接与信息流。以时序图描述系统从“采样-泵送-分析-数据输出”的完整自动化工作循环。

#### 4.2.2 边缘计算视觉传感模块的设计与实现

详细列出硬件清单，包括嵌入式计算平台、图像传感器、紧凑型光学镜头组以及可程控照明阵列。描述如何通过三维打印技术设计并制作模块化的、具有光路对齐和芯片固定功能的机械外壳。阐述将第三章训练的YOLO模型进行优化，并部署到该边缘设备上的软件流程。

#### 4.2.3 双通道微流控分析芯片的设计与制备

阐述芯片采用标准软光刻技术与PDMS-玻璃键合的制备方法。

##### 4.2.3.1 花粉动态捕获与成像芯片

提供该芯片的设计图，标注V型鱼骨微结构阵列的关键几何参数。解释其流体力学工作原理，即通过诱导横向微涡流实现对颗粒的减速与聚焦。

##### 4.2.3.2 自动化微流控免疫分析芯片

展示该芯片的多层复杂设计。阐述蛇形通道如何用于延长反应孵育时间；微柱阵列如何通过增大比表面积来增强信号；以及集成的试剂储库、混合器、清洗缓冲液通道和废液出口如何实现酶联免疫吸附测定流程的全自动化。

#### 4.2.4 集成系统性能验证方案

分步描述验证流程。(1) 模块验证：使用静态载玻片验证视觉模块的性能。(2) 通道一验证：泵入花粉悬液，验证V型鱼骨芯片的动态捕获与识别能力。(3) 通道二验证：泵入真菌标志物标准品，绘制剂量-反应曲线，计算微流控酶联免疫吸附测定的检测限和线性范围。

### 4.3 结果与讨论

#### 4.3.1 视觉传感模块的性能标定与算法部署验证

呈现紧凑型平台与科研级显微镜所采集图像的对比，并以表格形式量化对比YOLO模型在两套图像上的平均精度均值，证明小型化并未牺牲关键性能。

#### 4.3.2 花粉在线动态捕获与识别性能

展示花粉在V型鱼骨芯片中运动轨迹的序列图像，直观证明其减速效果。报告在连续流条件下，系统对花粉的捕获率与识别准确率。

#### 4.3.3 微流控免疫分析模块的灵敏度与自动化性能

呈现高质量的酶联免疫吸附测定剂量-反应曲线图，报告计算出的检测限值，并将其与传统96孔板法进行性能对比，突出其在灵民度、速度和试剂消耗上的优势。

#### 4.3.4 综合讨论：一个多模态、现场可部署系统的构建与价值

本节为升华部分。重点讨论“多模态”的科学价值：视觉通道提供了对花粉等大尺寸颗粒的形态学信息，而免疫分析通道则提供了对真菌等小尺寸、形态不可辨颗粒的生物化学信息。论述这两种正交的信息维度如何互为补充，使系统能够生成远比单一技术更全面、更可靠的空气致敏原图谱，从而实现从“监测”到“洞察”的飞跃。

### 4.4 本章小结与展望

#### 4.4.1 系统优势

总结本系统在集成度、自动化、小型化以及多模态检测能力方面的突破。

#### 4.4.2 系统局限性与工程挑战

深入讨论微流控通道的长期抗堵塞能力、生化试剂的板上长期稳定存储、流体交叉污染的风险控制，以及系统在复杂户外环境中的适应性等关键工程挑战。

---

## 第五章 总结与展望

### 5.1 全文核心结论总结

本节将以高度凝练的语言，分点回顾并重申全篇论文最关键的研究结论。例如：(1) 成功构建了一个双层优化框架，并通过超亲水表面工程显著提升了湿壁旋风采样器的性能，使其优于商业化标准设备。(2) 成功开发了一个基于深度学习的自动化识别模型，首次实现了对多种无染色致敏花粉的高精度、快速检测，其性能媲美甚至超越了传统金标准。(3) 成功设计并验证了一个集成化的微流控-视觉传感系统原型，首次在单一小型化平台中实现了对空气致敏原的“形态学-生物化学”多模态在线监测。

### 5.2 本研究的创新性与学术贡献

本节将从三个层面阐述本论文的独创性贡献。在理论与方法学层面，提出了“场景-装置”双层优化这一系统工程思想，以及“免染色识别”这一颠覆性分析范式。在技术与工具层面，贡献了一款性能卓越的新型采样器、一个高质量的开源花粉图像数据集、一个即用型的AI识别模型，以及两款功能创新的微流控芯片。在系统集成层面，构建了本领域首个具备多模态检测能力的自动化监测原型，为下一代环境健康传感器的发展提供了重要的技术蓝图和工程实践。

### 5.3 研究不足与未来研究方向

本节将综合各章节的局限性，并从更宏观的视角提出未来研究的展望。例如：(1) 拓展监测广度：将AI识别模型拓展至更多种类的花粉和真菌孢子，将免疫分析通道拓展至病毒等其他致敏原。(2) 提升系统鲁棒性：专注于解决微流控抗堵塞和试剂板上存储等关键工程难题，进行长期的户外实地测试。(3) 迈向网络化与智能化：将多个监测站点进行物联网部署，构建区域性的高时空分辨率致敏原预警网络，并结合气象数据与机器学习模型，实现从“监测”到“预测”的跨越。
