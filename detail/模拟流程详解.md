# 海关大厅通风系统CFD模拟流程详解

本文档详细阐述了用于分析海关大厅内部空气流动、热量传递及污染物扩散的计算流体动力学（CFD）模拟的全过程。

---

## 1. 模拟目的 (Simulation Objective)

本次模拟的核心目标是：
- **评估现有通风策略的有效性**：分析空调送风与新风系统联合作用下，能否有效组织气流，形成从检测人员区域流向通关人员区域的定向气流屏障。
- **分析污染物扩散路径**：追踪通关人员呼出的气态污染物（以CO2为示踪剂）和颗粒物的运动轨迹与浓度分布，识别潜在的交叉感染风险区域。
- **量化关键指标**：计算通风系统对呼出污染物的渗透率以及特定位置采样器的捕获效率，为优化防疫措施提供数据支持。

---

## 2. 第一步：几何建模与简化 (Geometry Modeling and Simplification)

在进行模拟前，首先需要构建场景的数字化三维模型。

- **建模软件**：使用专业的CAD软件（如 SolidWorks, AutoCAD, SpaceClaim 等）进行几何建模。
- **几何尺寸**：
  - 整体尺寸：两个集装箱拼接，总长30m，宽6m，高2.7m。
  - 内部结构：包含11个检测窗口（0.8m x 0.8m）、隔板、桌椅、人员模型。
  - 通风设备：5台分体式空调（送风口0.8m x 0.075m，回风口0.8m x 0.15m）和圆形新风口（直径0.3m）。
- **模型简化**：
  - **对称处理**：由于模型沿中心平面（x=15m）对称，为大幅减少计算量，仅建立一半模型进行计算，并在对称面上施加对称边界条件。
  - **特征忽略**：忽略对整体流场影响甚微的微小几何特征，如螺栓、倒角、电线等，以简化网格生成难度。

---

## 3. 第二步：网格划分 (Meshing)

网格质量直接决定了计算的精度和收敛性，是CFD分析中的关键步骤。

- **网格类型**：考虑到模型的复杂性，采用**非结构化多面体网格 (Polyhedral Mesh)**。这种网格对复杂外形的适应性好，且在相同单元数量下通常能获得比四面体网格更高的计算精度。
- **网格尺寸与加密**：
  - **全局尺寸**：设定一个基础网格尺寸以控制整体网格密度。
  - **局部加密**：在速度、温度、浓度梯度变化剧烈的关键区域进行网格加密，包括：
    - 所有送/回风口、人员口鼻部、检测窗口。
    - 人体模型周围，特别是热羽流可能产生的区域。
  - **边界层网格 (Inflation Layer)**：在所有壁面（包括人体、墙壁、设备表面）附近创建多层棱柱层网格，以精确捕捉近壁区域的流动细节。
- **网格质量检查**：检查网格的**歪斜度 (Skewness)**和**正交质量 (Orthogonal Quality)**等关键指标，确保网格质量满足求解器要求。
- **网格无关性验证**：为确保计算结果不受网格疏密影响，理论上应准备至少三套不同密度的网格（粗、中、细），并验证关键物理量（如窗口平均风速）在不同网格下趋于稳定。

---

## 4. 第三步：物理模型设置 (Physics Setup)

根据模拟需求，选择合适的物理模型。

- **求解器类型**：选用**稳态 (Steady-state)**、**压力基 (Pressure-based)**求解器。
- **流动模型**：
  - **湍流模型**：选择 **Realizable k-ε 模型**。该模型对于包含射流、强漩涡和二次流的室内通风模拟具有较高的精度和鲁棒性。
- **传热模型**：
  - **能量方程**：开启能量方程，模拟室内的热量传递。
  - **浮力效应**：为模拟温度差异引起的热浮力（自然对流），将空气密度设置为**理想气体 (Ideal Gas)**，使其随温度变化。
- **组分输运模型 (Species Transport)**：
  - 开启此模型以追踪CO2示踪气体的扩散。在材料中定义一个包含`air`和`co2`的多组分混合物。
- **离散相模型 (Discrete Phase Model, DPM)**：
  - 采用拉格朗日方法追踪颗粒物的运动轨迹。
  - **颗粒物定义**：设置颗粒物为从通关人员口鼻处释放的**惰性颗粒 (Inert)**，粒径1μm，密度998 kg/m³，质量流量1e-10 kg/s。
  - **物理作用**：考虑重力和空气对颗粒的曳力作用。

---

## 5. 第四步：边界条件设置 (Boundary Conditions)

边界条件的精确设定是模拟成功的保障。

- **入口边界**：
  - **空调器进风口**：`速度入口 (Velocity Inlet)`，速度大小 3 m/s，方向斜向下45°，温度 24℃。
  - **圆形新风口**：`速度入口 (Velocity Inlet)`，速度大小 3 m/s，温度 24℃。
  - **空调器回风口**：`速度入口 (Velocity Inlet)`，速度大小 -1.5 m/s（负值代表流出）。
  - **检测人员嘴巴 (吸气)**：`速度入口 (Velocity Inlet)`，速度大小 -1 m/s。
  - **通关人员嘴巴 (呼气)**：`速度入口 (Velocity Inlet)`，速度大小 1 m/s，温度 37℃，CO2体积分数 4%。此为污染物和颗粒物的释放源。
  - **采样器**：`质量流量出口 (Mass-Flow Outlet)`，流量大小根据200 L/min换算得到。
- **出口边界**：
  - **通关人员区域窗户**：`压力出口 (Pressure Outlet)`，表压设置为 0 Pa。
- **壁面边界**：
  - **人员皮肤**：`壁面 (Wall)`，设置为 `定热流密度 (Heat Flux)`，大小为 20 W/m²。
  - **顶棚、侧墙、地板**：`壁面 (Wall)`，设置为 `定热流密度 (Heat Flux)`，大小为 4 W/m²。
  - **中间对称面**：`对称边界 (Symmetry)`。
  - **其他所有壁面**：`壁面 (Wall)`，设置为 `绝热 (Adiabatic)`，热流密度为 0 W/m²。

---

## 6. 第五步：求解与收敛监控 (Solving and Convergence Monitoring)

- **求解算法**：
  - **压力-速度耦合**：采用 **SIMPLE** 算法。
  - **空间离散格式**：动量、湍流、能量和组分方程均采用**二阶迎风格式 (Second-Order Upwind)** 以保证计算精度。
- **初始化**：采用**混合初始化 (Hybrid Initialization)**，为流场提供一个合理的初始解。
- **收敛判据**：
  - **残差监控**：所有方程的计算残差均需降低到1e-5以下。
  - **物理量监控**：监控关键位置（如出口质量流量、室内平均温度）的物理量，当这些值不再随迭代步数发生变化时，可认为计算已收敛。

---

## 7. 第六步：后处理与结果分析 (Post-processing and Result Analysis)

计算收敛后，对结果数据进行可视化和分析。

- **创建云图、矢量图和流线图**：
  - **流场分析**：在不同截面创建速度云图和矢量图，展示气流方向和速度大小；创建流线图，直观显示空气从送到排的完整路径。
  - **温度场分析**：创建温度分布云图，评估室内热舒适性。
  - **污染物分析**：创建CO2浓度云图和等值面图，识别高浓度区域。
  - **颗粒物分析**：可视化颗粒物轨迹，分析其逃逸和沉积情况。
- **定量数据提取**：
  - 利用后处理软件的积分功能，精确计算通过采样器入口和检测窗口截面的污染物质量流量，即 `M_sam` 和 `M_win`。
  - 同样，计算通关人员口鼻处释放的污染物总质量流量 `M_mou`。
- **效率计算**：根据提取的数据，代入公式计算采样器捕获效率和污染物渗透率。

---

## 8. 效率计算核查 (Efficiency Calculation Check)

- **采样器捕获效率 \( E_{sam} \)**
  \[
  E_{sam} = \frac{M_{sam}}{M_{mou}} = \frac{1.4 \times 10^{-6}}{0.000344} \approx 0.00407 \approx 0.4\%
  \]
- **污染物渗透率 \( E_{win} \)**
  \[
  E_{win} = \frac{M_{win}}{M_{mou}} = \frac{2.8 \times 10^{-6}}{0.000344} \approx 0.00814 \approx 0.8\%
  \]

计算结果表明，基于模拟数据，原始文档中的计算是正确的。

---

## 附录1：关键数据溯源与合理性分析

如前文所述，效率计算的准确性依赖于三个核心数据：`M_mou`（呼出污染物总质量流量）、`M_sam`（采样器捕获的质量流量）和 `M_win`（通过窗口渗透的质量流量）。其中，`M_sam` 和 `M_win` 是仿真的直接输出结果，无法手动核算。但作为污染源头的 `M_mou`，我们可以对其进行合理性验证。

### `M_mou` 的来源核算

`M_mou` 是所有污染源（通关人员口部）释放的CO2总质量流量，其数值由CFD软件根据边界条件计算得出，公式为：

\[ M_{\text{mou}} = \rho_{\text{exhale}} \times A_{\text{total\_mouth}} \times v_{\text{exhale}} \times C_{\text{CO2\_mass}} \]

根据报告提供的数据进行分步解析：

1.  **呼出气流速度 (\(v_{\text{exhale}}\))**: **1 m/s** (来自边界条件)。
2.  **呼出气流温度**: 37℃ (310.15 K)。
3.  **CO2浓度**: 报告中为4%**体积分数**。进行单位换算：
    *   空气平均摩尔质量 ≈ 28.97 g/mol
    *   CO2摩尔质量 ≈ 44.01 g/mol
    *   换算后，4%体积分数约等于 **5.95%质量分数**。
4.  **呼出气流密度 (\(\rho_{\text{exhale}}\))**: 含有4%CO2的37℃湿润空气，密度约 **1.16 kg/m³**。
5.  **嘴部总面积 (\(A_{\text{total\_mouth}}\))**: 这是模型几何中的一个未明确给出的参数。我们可以利用已知 `M_mou` 值反推该面积，以验证其设定的合理性。

    \[
    A_{\text{total\_mouth}} = \frac{M_{\text{mou}}}{\rho_{\text{exhale}} \times v_{\text{exhale}} \times C_{\text{CO2\_mass}}} = \frac{0.000344 \text{ kg/s}}{1.16 \text{ kg/m}^3 \times 1 \text{ m/s} \times 0.0595} \approx 0.00498 \text{ m}^2
    \]

    *   该总面积为 **49.8 cm²**。
    *   模型为对称模型，计算区域内有 11 / 2 = **5.5个**污染源（通关人员）。
    *   因此，模型中为每个人设定的嘴部简化模型的面积为：
        \[ A_{\text{person}} = \frac{49.8 \text{ cm}^2}{5.5} \approx 9.05 \text{ cm}^2 \]

**结论**：人均约 **9 cm²** 的呼气口面积（相当于 3cm x 3cm 的正方形）是一个在CFD人体模型简化中非常常见且合理的设定。这证明了作为计算基础的 `M_mou = 0.000344 kg/s` 这个值，是在合理的模型参数下得到的，是**自洽且可信的**。

---

## 附录2：结果讨论——低捕获效率下的采样代表性分析

本次模拟得出的采样器捕获效率仅为 **0.4%**，渗透率也仅为 **0.8%**。这两个看似极低的数值，恰恰反映了在大型、强制通风空间内污染物扩散的真实物理规律，并引发了关于采样策略有效性的深入思考。

### 1. 为什么捕获效率如此之低？

- **巨大的空间稀释效应**：通关人员呼出的污染物（气溶胶）在呼出瞬间即被卷入室内宏观的主流场。海关大厅空间巨大，且有强大的空调和新风系统在运行，污染物会被迅速稀释到整个空间中，导致任意局部点的浓度都变得非常低。
- **通风系统的定向吹扫**：从结果可以看出，通风系统形成的气流屏障，其主要作用是将污染物“推”向通关人员区域的下游并最终排出室外，而不是将其“送”到采样器口。采样器只是被动地采集流经其所在位置的空气。
- **采样口的局限性**：采样器只有一个或几个固定位置的微小入口，相较于整个大厅的空气体量，它能“抽吸”到的范围极其有限。绝大部分污染物会随气流绕过采样器，或在到达之前就已经被排出。

### 2. 0.4%的捕获效率是否意味着采样失败或无意义？

**恰恰相反，这个结果非常有意义，并且采样过程是具备代表性的。**

- **代表性的核心是“比例”而非“总量”**：气溶胶采样的目的，通常不是为了捕获空间中100%的病毒颗粒，这在实际中既不可能也无必要。其核心目的是**判断空气中是否存在病毒**，以及**评估其大致的浓度水平**。只要采样器捕获的样本中，污染物的浓度与采样器周围环境空气中的浓度成正比，那么这个采样就是**有代表性的**。0.4%的效率说明采样器稳定地从环境中“舀取”了与其抽气能力相匹配的一部分空气，这部分空气的成分可以代表周围环境的平均状态。

- **低效率是大型通风空间采样的常态**：在医院病房、机场、车站等大型空间，气溶胶采样效率普遍处于极低的水平。这一数值本身就是对通风系统稀释能力的有力证明。一个高效的通风系统，其必然结果就是让任何固定点位的采样器都难以捕集到高浓度的污染物。

### 3. 低效率对实际防疫工作的启示

- **对检测技术灵敏度的极高要求**：0.4%的捕获率意味着，如果一个感染者呼出的病毒总量本身就不高，那么被采样器捕获的病毒数量将微乎其微。这就要求后端检测技术（如qPCR）必须具备极高的灵敏度，才能从极低浓度的样本中检出阳性。
- **采样时间和位置的重要性**：为了弥补低效率，可以通过**延长采样时间**来增加捕获污染物的总量，从而提高检出率。此外，通过CFD模拟可以找到污染物浓度相对较高的“热点区域”，在这些位置布设采样器能有效提高采样效率。
- **通风仍是最高效的防护手段**：模拟结果最直观地证明了，**源头控制和通风稀释**是远比被动采样更重要、更有效的防疫手段。0.8%的低渗透率说明通风系统成功将99.2%的污染物阻挡在了检测人员区域之外，这才是整个系统的核心价值所在。

综上所述，极低的捕获效率和渗透率非但不是模拟或采样的失败，反而是对现有通风防疫策略有效性的一次成功的量化评估。它科学地揭示了在当前设计下，通过定向气流可以极大地降低交叉感染风险，同时也为采样策略的优化和检测技术的选择提供了关键的数据支撑。
